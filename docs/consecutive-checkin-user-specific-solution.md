# User-Specific Consecutive Check-in Task Solution

## Problem Statement

The original implementation had consecutive check-in tasks (7-day and 30-day) set as inactive initially in the task seeder, with global activation when users completed milestones. This approach had several issues:

1. **Global State Problem**: When User A completed 3 days and activated the 7-day task, it became visible to ALL users, including User B who might only have 1 day streak.

2. **Inconsistent User Experience**: Users could see tasks they weren't eligible for based on their individual progress.

3. **Requirement Violation**: The requirement states "Only one task will be displayed at a time" per user, but the global activation made this impossible to enforce properly.

## Solution Overview

We implemented a **user-specific task filtering approach** that:

1. **Keeps all consecutive tasks active globally** in the database
2. **Filters tasks per-user** based on their current consecutive streak
3. **Shows only the appropriate task** for each user's progress level

## Implementation Details

### 1. Repository Layer Changes

**File**: `internal/repo/activity_cashback/activity_task_repository.go`

- Modified `GetTasksForUser()` to filter consecutive check-in tasks based on user's current streak
- Added `filterConsecutiveCheckInTasks()` method to determine which consecutive task to show
- Added `getUserConsecutiveStreak()` method to get user's current streak from progress

```go
// Logic for task visibility based on streak:
// - Streak 0-2: Show 3-day task
// - Streak 3-6: Show 7-day task  
// - Streak 7-29: Show 30-day task
// - Streak 30+: No consecutive task shown
```

### 2. Handler Layer Changes

**File**: `internal/service/activity_cashback/task_handlers.go`

- Deprecated `activateNextMilestoneTask()` method
- Removed global task activation logic
- Tasks are now automatically visible based on user progress

### 3. Seeder Changes

**File**: `internal/service/activity_cashback/task_seeder.go`

- Removed logic that set 7-day and 30-day tasks as inactive
- All consecutive tasks are now created as active by default
- Task visibility is controlled by filtering, not by IsActive status

## User Experience Flow

### Scenario 1: New User (0 days streak)
```
User sees: [3-day consecutive task]
User completes daily check-in for 3 days
→ 3-day task completed, user gets 50 points
→ User now sees: [7-day consecutive task]
```

### Scenario 2: Progressing User (5 days streak)
```
User sees: [7-day consecutive task] 
User continues daily check-in
→ At 7 days: 7-day task completed, user gets 200 points
→ User now sees: [30-day consecutive task]
```

### Scenario 3: Advanced User (15 days streak)
```
User sees: [30-day consecutive task]
User continues daily check-in
→ At 30 days: 30-day task completed, user gets 1000 points
→ User sees: [no more consecutive tasks]
```

## Benefits

### ✅ **Solved Problems**

1. **Individual User Experience**: Each user sees only tasks relevant to their progress
2. **No Global State Issues**: User A's progress doesn't affect User B's task visibility
3. **Requirement Compliance**: "Only one task displayed at a time" is now enforced per user
4. **Scalable**: Works for unlimited number of users without interference

### ✅ **Maintained Features**

1. **Progressive Unlocking**: Users still unlock higher-tier tasks by completing lower ones
2. **Streak Tracking**: Individual streak counting continues to work
3. **Point Rewards**: Milestone rewards (50/200/1000 points) remain unchanged
4. **Reset Logic**: Streak resets still work when users miss days

## Technical Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   User Request  │───▶│  GetTasksForUser │───▶│ Filter by Streak│
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │ All Active Tasks │    │ User-Specific   │
                       │ (3/7/30 day)     │    │ Task List       │
                       └──────────────────┘    └─────────────────┘
```

## Database Impact

- **No schema changes required**
- **No new tables needed**
- **Existing data remains valid**
- **Migration**: Simply update existing inactive tasks to active

## Testing Strategy

Created comprehensive tests in `consecutive_checkin_user_specific_test.go`:

1. **User-specific visibility tests**
2. **Filtering logic verification**
3. **Multiple users independence tests**
4. **Edge cases (0 streak, 30+ streak)**

## Migration Steps

1. **Update existing inactive tasks to active**:
   ```sql
   UPDATE activity_tasks 
   SET is_active = true 
   WHERE task_identifier IN ('consecutive_checkin_7', 'consecutive_checkin_30');
   ```

2. **Deploy new code**
3. **Verify user-specific task visibility**

## Future Enhancements

1. **Caching**: Add Redis caching for user streak data
2. **Analytics**: Track task visibility patterns per user segment
3. **A/B Testing**: Test different milestone thresholds
4. **Personalization**: Customize task visibility based on user behavior

## Conclusion

This solution provides a robust, scalable approach to user-specific task visibility while maintaining all existing functionality and improving user experience. Each user now has their own personalized task progression journey without interference from other users' progress.
