package activity_cashback

import (
	"context"
	"fmt"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity_cashback"
	"go.uber.org/zap"
)

// TaskSeeder seeds initial tasks into the database
type TaskSeeder struct {
	categoryRepo activity_cashback.TaskCategoryRepositoryInterface
	taskRepo     activity_cashback.ActivityTaskRepositoryInterface
}

// NewTaskSeeder creates a new TaskSeeder
func NewTaskSeeder() *TaskSeeder {
	return &TaskSeeder{
		categoryRepo: activity_cashback.NewTaskCategoryRepository(),
		taskRepo:     activity_cashback.NewActivityTaskRepository(),
	}
}

// SeedTasks seeds all initial tasks
func (s *TaskSeeder) SeedTasks(ctx context.Context) error {
	global.GVA_LOG.Info("Starting task seeding")

	// Get categories
	categories, err := s.categoryRepo.GetAll(ctx)
	if err != nil {
		return err
	}

	categoryMap := make(map[string]uint)
	for _, category := range categories {
		categoryMap[string(category.Name)] = category.ID
	}

	// Seed daily tasks
	if err := s.seedDailyTasks(ctx, categoryMap["daily"]); err != nil {
		return err
	}

	// Seed community tasks
	if err := s.seedCommunityTasks(ctx, categoryMap["community"]); err != nil {
		return err
	}

	// Seed trading tasks
	if err := s.seedTradingTasks(ctx, categoryMap["trading"]); err != nil {
		return err
	}

	global.GVA_LOG.Info("Task seeding completed successfully")
	return nil
}

// seedDailyTasks seeds daily tasks
func (s *TaskSeeder) seedDailyTasks(ctx context.Context, categoryID uint) error {
	dailyTasks := []struct {
		name               string
		description        string
		taskIdentifier     model.TaskIdentifier
		points             int
		frequency          model.TaskFrequency
		resetPeriod        *model.ResetPeriod
		conditions         *model.TaskConditions
		actionTarget       *string
		verificationMethod *model.VerificationMethod
		taskIcon           *string
		buttonText         *string
		sortOrder          int
	}{
		{
			name:           "Daily Login",
			description:    "Refresh after UTC 0:00, log in to claim",
			taskIdentifier: model.TaskIDDailyCheckin,
			points:         5,

			frequency:          model.FrequencyDaily,
			taskIcon:           &[]string{"📅"}[0],
			buttonText:         &[]string{"view"}[0],
			resetPeriod:        &[]model.ResetPeriod{model.ResetDaily}[0],
			actionTarget:       &[]string{"homePage"}[0],
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			sortOrder:          1,
		},
		{
			name:               "Complete one meme transaction",
			description:        "Complete one MEME transaction between UTC 0:00-23:59",
			taskIdentifier:     model.TaskIDMemeTradeDaily,
			points:             200,
			frequency:          model.FrequencyDaily,
			taskIcon:           &[]string{"💰"}[0],
			buttonText:         &[]string{"trade"}[0],
			resetPeriod:        &[]model.ResetPeriod{model.ResetDaily}[0],
			conditions:         &model.TaskConditions{RequiredTradeCount: &[]int{1}[0]},
			actionTarget:       &[]string{"memeTrade"}[0],
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			sortOrder:          2,
		},
		{
			name:           "Complete one contract transaction",
			description:    "Complete one contract opening or closing between UTC 0:00-23:59",
			taskIdentifier: model.TaskIDPerpetualTradeDaily,
			points:         200,

			frequency:          model.FrequencyDaily,
			taskIcon:           &[]string{"💰"}[0],
			buttonText:         &[]string{"trade"}[0],
			resetPeriod:        &[]model.ResetPeriod{model.ResetDaily}[0],
			conditions:         &model.TaskConditions{RequiredTradeCount: &[]int{1}[0]},
			actionTarget:       &[]string{"FuturesTrade"}[0],
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			sortOrder:          3,
		},
		{
			name:               "View market page",
			description:        "View the market page to stay updated",
			taskIdentifier:     model.TaskIDMarketPageView,
			points:             5,
			frequency:          model.FrequencyDaily,
			taskIcon:           &[]string{"📊"}[0],
			buttonText:         &[]string{"view"}[0],
			resetPeriod:        &[]model.ResetPeriod{model.ResetDaily}[0],
			actionTarget:       &[]string{"market"}[0],
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			sortOrder:          4,
		},
		{
			name:               "Check Market Trends",
			description:        "Visited the homepage between UTC 0:00–23:59",
			taskIdentifier:     model.TaskIDCheckMarketTrends,
			points:             5,
			frequency:          model.FrequencyDaily,
			taskIcon:           &[]string{"📈"}[0],
			buttonText:         &[]string{"view"}[0],
			resetPeriod:        &[]model.ResetPeriod{model.ResetDaily}[0],
			actionTarget:       &[]string{"memeHome"}[0],
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			sortOrder:          5,
		},
		{
			name:               "Log in continuously for 3 days",
			description:        "Consecutive sign-ins for 3 days to earn 50 points",
			taskIdentifier:     model.TaskIDConsecutiveCheckin3,
			points:             50,
			frequency:          model.FrequencyProgressive,
			taskIcon:           &[]string{"📅"}[0],
			buttonText:         &[]string{"view"}[0],
			conditions:         &model.TaskConditions{ConsecutiveDays: &[]int{3}[0]},
			actionTarget:       &[]string{"homePage"}[0],
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			sortOrder:          6,
		},
		{
			name:               "Log in continuously for 7 days",
			description:        "Consecutive sign-ins for 7 days to earn 200 points",
			taskIdentifier:     model.TaskIDConsecutiveCheckin7,
			points:             200,
			frequency:          model.FrequencyProgressive,
			taskIcon:           &[]string{"📅"}[0],
			buttonText:         &[]string{"view"}[0],
			conditions:         &model.TaskConditions{ConsecutiveDays: &[]int{7}[0]},
			actionTarget:       &[]string{"homePage"}[0],
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			sortOrder:          7,
		},
		{
			name:               "Log in continuously for 30 days",
			description:        "Consecutive sign-ins for 30 days to earn 1000 points",
			taskIdentifier:     model.TaskIDConsecutiveCheckin30,
			points:             1000,
			frequency:          model.FrequencyProgressive,
			taskIcon:           &[]string{"📅"}[0],
			buttonText:         &[]string{"view"}[0],
			conditions:         &model.TaskConditions{ConsecutiveDays: &[]int{30}[0]},
			actionTarget:       &[]string{"homePage"}[0],
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			sortOrder:          8,
		},
	}

	for _, taskData := range dailyTasks {
		if err := s.createTaskFromDefinition(ctx, categoryID, taskData.taskIdentifier, taskData.frequency, taskData.resetPeriod, taskData.conditions, taskData.actionTarget, taskData.verificationMethod, nil, taskData.sortOrder); err != nil {
			return err
		}

		// Update task with icon and button text if provided
		if taskData.taskIcon != nil || taskData.buttonText != nil {
			if err := s.updateTaskIconAndButtonText(ctx, categoryID, taskData.taskIdentifier, taskData.taskIcon, taskData.buttonText); err != nil {
				return err
			}
		}
	}

	return nil
}

// seedCommunityTasks seeds community tasks
func (s *TaskSeeder) seedCommunityTasks(ctx context.Context, categoryID uint) error {
	communityTasks := []struct {
		name               string
		description        string
		taskIdentifier     *model.TaskIdentifier
		points             int
		frequency          model.TaskFrequency
		verificationMethod *model.VerificationMethod
		externalLink       *string
		actionTarget       *string
		taskIcon           *string
		buttonText         *string
		sortOrder          int
	}{
		{
			name:               "Follow on Twitter",
			description:        "Points awarded 2 minutes after clicking",
			taskIdentifier:     &[]model.TaskIdentifier{model.TaskIDTwitterFollow}[0],
			points:             50,
			frequency:          model.FrequencyOneTime,
			verificationMethod: &[]model.VerificationMethod{model.VerificationClickVerify}[0],
			externalLink:       &[]string{"https://x.com/intent/follow?screen_name=XBITDEX"}[0], // Default to English, will be handled by frontend based on language
			taskIcon:           &[]string{"🐦"}[0],
			buttonText:         &[]string{"follow"}[0],
			sortOrder:          1,
		},
		{
			name:               "Retweet",
			description:        "Points awarded 2 minutes after clicking",
			taskIdentifier:     &[]model.TaskIdentifier{model.TaskIDTwitterRetweet}[0],
			points:             10,
			frequency:          model.FrequencyManual,
			verificationMethod: &[]model.VerificationMethod{model.VerificationClickVerify}[0],
			externalLink:       &[]string{"https://x.com/intent/retweet?tweet_id=19522412291666035139"}[0],
			taskIcon:           &[]string{"🔄"}[0],
			buttonText:         &[]string{"share"}[0],
			sortOrder:          2,
		},
		{
			name:               "Like Tweet",
			description:        "Points awarded 2 minutes after clicking",
			taskIdentifier:     &[]model.TaskIdentifier{model.TaskIDTwitterLike}[0],
			points:             10,
			frequency:          model.FrequencyManual,
			verificationMethod: &[]model.VerificationMethod{model.VerificationClickVerify}[0],
			externalLink:       &[]string{"https://x.com/intent/like?tweet_id=19522412291666035139"}[0],
			taskIcon:           &[]string{"❤️"}[0],
			buttonText:         &[]string{"view"}[0],
			sortOrder:          3,
		},
		{
			name:               "Join Telegram",
			description:        "Points awarded 2 minutes after clicking",
			taskIdentifier:     &[]model.TaskIdentifier{model.TaskIDTelegramJoin}[0],
			points:             30,
			frequency:          model.FrequencyOneTime,
			verificationMethod: &[]model.VerificationMethod{model.VerificationClickVerify}[0],
			externalLink:       &[]string{"https://t.me/xbit_dex"}[0],
			taskIcon:           &[]string{"📱"}[0],
			buttonText:         &[]string{"join"}[0],
			sortOrder:          4,
		},
		{
			name:               "Invite Friends",
			description:        "Inviting friends counts as one only if they have trading records on the xbit platform",
			taskIdentifier:     &[]model.TaskIdentifier{model.TaskIDInviteFriends}[0],
			points:             100,
			frequency:          model.FrequencyUnlimited,
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			taskIcon:           &[]string{"👥"}[0],
			buttonText:         &[]string{"share"}[0],
			sortOrder:          5,
		},
		{
			name:               "Share Earnings Chart",
			description:        "Trigger by sharing earnings chart",
			taskIdentifier:     &[]model.TaskIdentifier{model.TaskIDShareEarningsChart}[0],
			points:             10,
			frequency:          model.FrequencyDaily,
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			actionTarget:       &[]string{"FuturesTrade"}[0],
			taskIcon:           &[]string{"📊"}[0],
			buttonText:         &[]string{"share"}[0],
			sortOrder:          6,
		},
	}

	for _, taskData := range communityTasks {
		resetPeriod := model.ResetDaily
		if taskData.frequency == model.FrequencyDaily {
			resetPeriod = model.ResetDaily
		} else {
			resetPeriod = model.ResetNever
		}

		if err := s.createTaskIfNotExistsWithIcon(ctx, categoryID, taskData.name, taskData.description, taskData.taskIdentifier, taskData.points, taskData.frequency, &resetPeriod, nil, taskData.actionTarget, taskData.verificationMethod, taskData.externalLink, taskData.taskIcon, taskData.buttonText, taskData.sortOrder); err != nil {
			return err
		}
	}

	return nil
}

// seedTradingTasks seeds trading tasks
func (s *TaskSeeder) seedTradingTasks(ctx context.Context, categoryID uint) error {
	// First, create tasks with TaskIdentifier using the new method
	tradingTasksWithIdentifier := []struct {
		identifier         model.TaskIdentifier
		frequency          model.TaskFrequency
		resetPeriod        *model.ResetPeriod
		conditions         *model.TaskConditions
		actionTarget       *string
		verificationMethod *model.VerificationMethod
		taskIcon           *string
		buttonText         *string
		sortOrder          int
	}{
		{
			identifier:         model.TaskIDTradingPoints,
			frequency:          model.FrequencyDaily,
			resetPeriod:        &[]model.ResetPeriod{model.ResetDaily}[0],
			actionTarget:       &[]string{"memeTrade"}[0],
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			taskIcon:           &[]string{"📊"}[0],
			buttonText:         &[]string{"trade"}[0],
			sortOrder:          1,
		},
	}

	// Create tasks with identifiers
	for _, taskData := range tradingTasksWithIdentifier {
		if err := s.createTaskFromDefinitionWithIcon(ctx, categoryID, taskData.identifier, taskData.frequency, taskData.resetPeriod, taskData.conditions, taskData.actionTarget, taskData.verificationMethod, nil, taskData.taskIcon, taskData.buttonText, taskData.sortOrder); err != nil {
			return fmt.Errorf("failed to create trading task with identifier %s: %w", taskData.identifier, err)
		}
	}

	// Then create accumulated trading tasks with identifiers
	accumulatedTradingTasks := []struct {
		identifier         model.TaskIdentifier
		frequency          model.TaskFrequency
		resetPeriod        *model.ResetPeriod
		conditions         *model.TaskConditions
		actionTarget       *string
		verificationMethod *model.VerificationMethod
		taskIcon           *string
		buttonText         *string
		sortOrder          int
	}{
		{
			identifier:         model.TaskIDAccumulatedTrading10K,
			frequency:          model.FrequencyProgressive,
			resetPeriod:        &[]model.ResetPeriod{model.ResetNever}[0],
			conditions:         &model.TaskConditions{MinTradingVolume: &[]float64{10000}[0]},
			actionTarget:       &[]string{"memeTrade"}[0],
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			taskIcon:           &[]string{"🎯"}[0],
			buttonText:         &[]string{"trade"}[0],
			sortOrder:          2,
		},
		{
			identifier:         model.TaskIDAccumulatedTrading50K,
			frequency:          model.FrequencyProgressive,
			resetPeriod:        &[]model.ResetPeriod{model.ResetNever}[0],
			conditions:         &model.TaskConditions{MinTradingVolume: &[]float64{50000}[0]},
			actionTarget:       &[]string{"memeTrade"}[0],
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			taskIcon:           &[]string{"🎯"}[0],
			buttonText:         &[]string{"trade"}[0],
			sortOrder:          3,
		},
		{
			identifier:         model.TaskIDAccumulatedTrading100K,
			frequency:          model.FrequencyProgressive,
			resetPeriod:        &[]model.ResetPeriod{model.ResetNever}[0],
			conditions:         &model.TaskConditions{MinTradingVolume: &[]float64{100000}[0]},
			actionTarget:       &[]string{"memeTrade"}[0],
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			taskIcon:           &[]string{"🎯"}[0],
			buttonText:         &[]string{"trade"}[0],
			sortOrder:          4,
		},
		{
			identifier:         model.TaskIDAccumulatedTrading500K,
			frequency:          model.FrequencyProgressive,
			resetPeriod:        &[]model.ResetPeriod{model.ResetNever}[0],
			conditions:         &model.TaskConditions{MinTradingVolume: &[]float64{500000}[0]},
			actionTarget:       &[]string{"memeTrade"}[0],
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			taskIcon:           &[]string{"🎯"}[0],
			buttonText:         &[]string{"trade"}[0],
			sortOrder:          5,
		},
	}

	// Create accumulated trading tasks with identifiers
	for _, taskData := range accumulatedTradingTasks {
		if err := s.createTaskFromDefinitionWithIcon(ctx, categoryID, taskData.identifier, taskData.frequency, taskData.resetPeriod, taskData.conditions, taskData.actionTarget, taskData.verificationMethod, nil, taskData.taskIcon, taskData.buttonText, taskData.sortOrder); err != nil {
			return fmt.Errorf("failed to create accumulated trading task with identifier %s: %w", taskData.identifier, err)
		}
	}

	return nil
}

// createTaskFromDefinition creates a task from its definition if it doesn't already exist
func (s *TaskSeeder) createTaskFromDefinition(ctx context.Context, categoryID uint, identifier model.TaskIdentifier, frequency model.TaskFrequency, resetPeriod *model.ResetPeriod, conditions *model.TaskConditions, actionTarget *string, verificationMethod *model.VerificationMethod, externalLink *string, sortOrder int) error {
	// Get task definition
	definition, exists := model.GetTaskDefinition(identifier)
	if !exists {
		return fmt.Errorf("task definition not found: %s", identifier)
	}

	// Check if task already exists by identifier
	tasks, err := s.taskRepo.GetByCategoryID(ctx, categoryID)
	if err != nil {
		return fmt.Errorf("failed to get tasks by category: %w", err)
	}

	// Check if task with this identifier already exists
	for _, task := range tasks {
		if task.TaskIdentifier != nil && *task.TaskIdentifier == identifier {
			// Task already exists, skip creation
			return nil
		}
	}

	// Create new task from definition
	task := &model.ActivityTask{
		CategoryID:  categoryID,
		Name:        definition.DisplayName,
		Description: &definition.Description,

		Frequency:          frequency,
		TaskIdentifier:     &identifier,
		Points:             definition.Points,
		ResetPeriod:        resetPeriod,
		Conditions:         conditions,
		ActionTarget:       actionTarget,
		VerificationMethod: verificationMethod,
		ExternalLink:       externalLink,
		IsActive:           true,
		SortOrder:          sortOrder,
	}

	if err := s.taskRepo.Create(ctx, task); err != nil {
		return fmt.Errorf("failed to create task %s: %w", identifier, err)
	}

	global.GVA_LOG.Info("Task created successfully",
		zap.String("identifier", string(identifier)),
		zap.String("name", definition.DisplayName))
	return nil
}

// createTaskFromDefinitionWithIcon creates a task from TaskDefinitionRegistry with icon and button text
func (s *TaskSeeder) createTaskFromDefinitionWithIcon(ctx context.Context, categoryID uint, identifier model.TaskIdentifier, frequency model.TaskFrequency, resetPeriod *model.ResetPeriod, conditions *model.TaskConditions, actionTarget *string, verificationMethod *model.VerificationMethod, externalLink *string, taskIcon *string, buttonText *string, sortOrder int) error {
	// Get task definition
	definition, exists := model.GetTaskDefinition(identifier)
	if !exists {
		return fmt.Errorf("task definition not found: %s", identifier)
	}

	// Check if task already exists by identifier
	tasks, err := s.taskRepo.GetByCategoryID(ctx, categoryID)
	if err != nil {
		return fmt.Errorf("failed to get tasks by category: %w", err)
	}

	// Check if task with this identifier already exists
	for _, task := range tasks {
		if task.TaskIdentifier != nil && *task.TaskIdentifier == identifier {
			// Task already exists, skip creation
			return nil
		}
	}

	// Create new task from definition
	task := &model.ActivityTask{
		CategoryID:  categoryID,
		Name:        definition.DisplayName,
		Description: &definition.Description,

		Frequency:          frequency,
		TaskIdentifier:     &identifier,
		Points:             definition.Points,
		ResetPeriod:        resetPeriod,
		Conditions:         conditions,
		ActionTarget:       actionTarget,
		VerificationMethod: verificationMethod,
		ExternalLink:       externalLink,
		TaskIcon:           taskIcon,
		ButtonText:         buttonText,
		IsActive:           true,
		SortOrder:          sortOrder,
	}

	if err := s.taskRepo.Create(ctx, task); err != nil {
		return fmt.Errorf("failed to create task %s: %w", identifier, err)
	}

	global.GVA_LOG.Info("Task created successfully",
		zap.String("identifier", string(identifier)),
		zap.String("name", definition.DisplayName))
	return nil
}

// createTaskIfNotExists creates a task if it doesn't already exist (legacy method)
func (s *TaskSeeder) createTaskIfNotExists(ctx context.Context, categoryID uint, name, description string, taskIdentifier *model.TaskIdentifier, points int, frequency model.TaskFrequency, resetPeriod *model.ResetPeriod, conditions *model.TaskConditions, actionTarget *string, verificationMethod *model.VerificationMethod, externalLink *string, sortOrder int) error {
	// Check if task already exists
	tasks, err := s.taskRepo.GetByCategoryID(ctx, categoryID)
	if err != nil {
		return err
	}

	for _, task := range tasks {
		if task.Name == name {
			global.GVA_LOG.Debug("Task already exists, skipping", zap.String("name", name))
			return nil
		}
	}

	// Create new task
	task := &model.ActivityTask{
		CategoryID:  categoryID,
		Name:        name,
		Description: &description,

		Frequency:          frequency,
		TaskIdentifier:     taskIdentifier,
		Points:             points,
		ResetPeriod:        resetPeriod,
		Conditions:         conditions,
		ActionTarget:       actionTarget,
		VerificationMethod: verificationMethod,
		ExternalLink:       externalLink,
		IsActive:           true,
		SortOrder:          sortOrder,
	}

	if err := s.taskRepo.Create(ctx, task); err != nil {
		global.GVA_LOG.Error("Failed to create task", zap.Error(err), zap.String("name", name))
		return err
	}

	global.GVA_LOG.Info("Task created successfully", zap.String("name", name), zap.String("task_id", task.ID.String()))
	return nil
}

// updateTaskIconAndButtonText updates task icon and button text for existing tasks
func (s *TaskSeeder) updateTaskIconAndButtonText(ctx context.Context, categoryID uint, identifier model.TaskIdentifier, taskIcon *string, buttonText *string) error {
	// Get tasks by category
	tasks, err := s.taskRepo.GetByCategoryID(ctx, categoryID)
	if err != nil {
		return fmt.Errorf("failed to get tasks by category: %w", err)
	}

	// Find task with matching identifier
	for _, task := range tasks {
		if task.TaskIdentifier != nil && *task.TaskIdentifier == identifier {
			// Update icon and button text
			if taskIcon != nil {
				task.TaskIcon = taskIcon
			}
			if buttonText != nil {
				task.ButtonText = buttonText
			}

			// Update the task
			if err := s.taskRepo.Update(ctx, &task); err != nil {
				return fmt.Errorf("failed to update task %s: %w", identifier, err)
			}

			global.GVA_LOG.Info("Task icon and button text updated successfully",
				zap.String("identifier", string(identifier)),
				zap.String("name", task.Name))
			return nil
		}
	}

	return fmt.Errorf("task with identifier %s not found", identifier)
}

// createTaskIfNotExistsWithIcon creates a task if it doesn't already exist with icon and button text support
func (s *TaskSeeder) createTaskIfNotExistsWithIcon(ctx context.Context, categoryID uint, name, description string, taskIdentifier *model.TaskIdentifier, points int, frequency model.TaskFrequency, resetPeriod *model.ResetPeriod, conditions *model.TaskConditions, actionTarget *string, verificationMethod *model.VerificationMethod, externalLink *string, taskIcon *string, buttonText *string, sortOrder int) error {
	// Check if task already exists
	tasks, err := s.taskRepo.GetByCategoryID(ctx, categoryID)
	if err != nil {
		return err
	}

	for _, task := range tasks {
		if task.Name == name {
			global.GVA_LOG.Debug("Task already exists, skipping", zap.String("name", name))
			return nil
		}
	}

	// Create new task
	task := &model.ActivityTask{
		CategoryID:  categoryID,
		Name:        name,
		Description: &description,

		Frequency:          frequency,
		TaskIdentifier:     taskIdentifier,
		Points:             points,
		ResetPeriod:        resetPeriod,
		Conditions:         conditions,
		ActionTarget:       actionTarget,
		VerificationMethod: verificationMethod,
		ExternalLink:       externalLink,
		TaskIcon:           taskIcon,
		ButtonText:         buttonText,
		IsActive:           true,
		SortOrder:          sortOrder,
	}

	if err := s.taskRepo.Create(ctx, task); err != nil {
		global.GVA_LOG.Error("Failed to create task", zap.Error(err), zap.String("name", name))
		return err
	}

	global.GVA_LOG.Info("Task created successfully", zap.String("name", name), zap.String("task_id", task.ID.String()))
	return nil
}

// createTaskIfNotExistsWithMultiLang creates a task if it doesn't already exist with multi-language support
func (s *TaskSeeder) createTaskIfNotExistsWithMultiLang(ctx context.Context, categoryID uint, name, nameCN, nameVN, description string, taskIdentifier *model.TaskIdentifier, points int, frequency model.TaskFrequency, resetPeriod *model.ResetPeriod, conditions *model.TaskConditions, actionTarget *string, verificationMethod *model.VerificationMethod, externalLink *string, taskIcon *string, buttonText *string, sortOrder int) error {
	// Check if task already exists
	tasks, err := s.taskRepo.GetByCategoryID(ctx, categoryID)
	if err != nil {
		return err
	}

	for _, task := range tasks {
		if task.Name == name {
			global.GVA_LOG.Debug("Task already exists, skipping", zap.String("name", name))
			return nil
		}
	}

	// Create multilingual name
	multilingualName := &model.MultilingualName{
		En: name,
	}
	if nameCN != "" {
		multilingualName.Zh = &nameCN
	}
	if nameVN != "" {
		multilingualName.Vi = &nameVN
	}

	// Create new task
	task := &model.ActivityTask{
		CategoryID:  categoryID,
		Name:        name, // Keep for backward compatibility
		Description: &description,

		Frequency:          frequency,
		TaskIdentifier:     taskIdentifier,
		Points:             points,
		ResetPeriod:        resetPeriod,
		Conditions:         conditions,
		ActionTarget:       actionTarget,
		VerificationMethod: verificationMethod,
		ExternalLink:       externalLink,
		TaskIcon:           taskIcon,
		ButtonText:         buttonText,
		IsActive:           true,
		SortOrder:          sortOrder,
	}

	// Set multilingual name
	task.SetMultilingualName(multilingualName)

	if err := s.taskRepo.Create(ctx, task); err != nil {
		global.GVA_LOG.Error("Failed to create task", zap.Error(err), zap.String("name", name))
		return err
	}

	global.GVA_LOG.Info("Task created successfully with multi-language support",
		zap.String("name", name),
		zap.String("name_cn", nameCN),
		zap.String("name_vn", nameVN),
		zap.String("task_id", task.ID.String()))
	return nil
}

// setTaskInactive sets a task as inactive
func (s *TaskSeeder) setTaskInactive(ctx context.Context, categoryID uint, taskIdentifier model.TaskIdentifier) error {
	// Find the task by identifier using GetAll to include inactive tasks
	allTasks, err := s.taskRepo.GetAll(ctx)
	if err != nil {
		return fmt.Errorf("failed to get all tasks: %w", err)
	}

	for _, task := range allTasks {
		if task.CategoryID == categoryID && task.TaskIdentifier != nil && *task.TaskIdentifier == taskIdentifier {
			task.IsActive = false
			if err := s.taskRepo.Update(ctx, &task); err != nil {
				return fmt.Errorf("failed to set task inactive: %w", err)
			}
			global.GVA_LOG.Info("Task set to inactive",
				zap.String("task_identifier", string(taskIdentifier)),
				zap.String("task_id", task.ID.String()))
			break
		}
	}

	return nil
}
