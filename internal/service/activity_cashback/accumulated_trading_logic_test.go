package activity_cashback

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"go.uber.org/zap"
)

// AccumulatedTradingLogicTestSuite tests the logic without database dependencies
type AccumulatedTradingLogicTestSuite struct {
	suite.Suite
	mockService *MockActivityCashbackService
	ctx         context.Context
}

func (suite *AccumulatedTradingLogicTestSuite) SetupTest() {
	suite.ctx = context.Background()
	suite.mockService = &MockActivityCashbackService{}

	// Initialize logger to prevent nil pointer dereference
	if global.GVA_LOG == nil {
		global.GVA_LOG = zap.NewNop() // No-op logger for tests
	}
}

func (suite *AccumulatedTradingLogicTestSuite) TestAccumulatedTradingHandler_Logic() {
	testUserID := uuid.New()
	testTaskID := uuid.New()

	// Test case 1: Volume below milestone - should not complete
	suite.T().Run("Volume below milestone", func(t *testing.T) {
		// Setup mocks
		mockProgress := &model.UserTaskProgress{
			UserID:        testUserID,
			TaskID:        testTaskID,
			ProgressValue: 5000, // Current progress
			Status:        model.TaskStatusInProgress,
		}

		mockTierInfo := &model.UserTierInfo{
			UserID:           testUserID,
			TradingVolumeUSD: decimal.NewFromFloat(8000), // Below 10K milestone
		}

		mockTask := &model.ActivityTask{
			ID:     testTaskID,
			Points: 300,
		}

		suite.mockService.On("GetTaskProgress", mock.Anything, testUserID, testTaskID).Return(mockProgress, nil)
		suite.mockService.On("GetUserTierInfo", mock.Anything, testUserID).Return(mockTierInfo, nil)
		suite.mockService.On("SetProgress", mock.Anything, testUserID, testTaskID, 8000).Return(nil)

		// Create handler
		handler := NewAccumulatedTradingHandler(suite.mockService, model.TaskIDAccumulatedTrading10K, 10000)

		// Execute
		err := handler.Handle(suite.ctx, testUserID, mockTask, map[string]interface{}{})

		// Verify
		suite.NoError(err)
		suite.mockService.AssertExpectations(t)

		// Should NOT call CompleteTaskWithPoints
		suite.mockService.AssertNotCalled(t, "CompleteTaskWithPoints")
	})

	// Test case 2: Volume above milestone - should complete
	suite.T().Run("Volume above milestone", func(t *testing.T) {
		// Reset mock
		suite.mockService = &MockActivityCashbackService{}

		mockProgress := &model.UserTaskProgress{
			UserID:        testUserID,
			TaskID:        testTaskID,
			ProgressValue: 8000, // Current progress
			Status:        model.TaskStatusInProgress,
		}

		mockTierInfo := &model.UserTierInfo{
			UserID:           testUserID,
			TradingVolumeUSD: decimal.NewFromFloat(15000), // Above 10K milestone
		}

		mockTask := &model.ActivityTask{
			ID:     testTaskID,
			Points: 300,
		}

		suite.mockService.On("GetTaskProgress", mock.Anything, testUserID, testTaskID).Return(mockProgress, nil)
		suite.mockService.On("GetUserTierInfo", mock.Anything, testUserID).Return(mockTierInfo, nil)
		suite.mockService.On("SetProgress", mock.Anything, testUserID, testTaskID, 10000).Return(nil)
		suite.mockService.On("CompleteTaskWithPoints", mock.Anything, testUserID, testTaskID, mock.MatchedBy(func(data map[string]interface{}) bool {
			milestone, ok := data["milestone"].(float64)
			return ok && milestone == 10000
		})).Return(nil)

		// Create handler
		handler := NewAccumulatedTradingHandler(suite.mockService, model.TaskIDAccumulatedTrading10K, 10000)

		// Execute
		err := handler.Handle(suite.ctx, testUserID, mockTask, map[string]interface{}{})

		// Verify
		suite.NoError(err)
		suite.mockService.AssertExpectations(t)
	})

	// Test case 3: Already completed - should skip
	suite.T().Run("Already completed", func(t *testing.T) {
		// Reset mock
		suite.mockService = &MockActivityCashbackService{}

		mockProgress := &model.UserTaskProgress{
			UserID:        testUserID,
			TaskID:        testTaskID,
			ProgressValue: 10000,
			Status:        model.TaskStatusCompleted, // Already completed
		}

		suite.mockService.On("GetTaskProgress", mock.Anything, testUserID, testTaskID).Return(mockProgress, nil)

		// Create handler
		handler := NewAccumulatedTradingHandler(suite.mockService, model.TaskIDAccumulatedTrading10K, 10000)

		mockTask := &model.ActivityTask{
			ID:     testTaskID,
			Points: 300,
		}

		// Execute
		err := handler.Handle(suite.ctx, testUserID, mockTask, map[string]interface{}{})

		// Verify
		suite.NoError(err)
		suite.mockService.AssertExpectations(t)

		// Should NOT call GetUserTierInfo or other methods
		suite.mockService.AssertNotCalled(t, "GetUserTierInfo")
		suite.mockService.AssertNotCalled(t, "SetProgress")
		suite.mockService.AssertNotCalled(t, "CompleteTaskWithPoints")
	})
}

func (suite *AccumulatedTradingLogicTestSuite) TestAccumulatedTradingProcessor_Logic() {
	testUserID := uuid.New()
	testTaskID := uuid.New()

	// Test case: MEME trade with volume above milestone
	suite.T().Run("MEME trade above milestone", func(t *testing.T) {
		// Setup mocks
		mockProgress := &model.UserTaskProgress{
			UserID:        testUserID,
			TaskID:        testTaskID,
			ProgressValue: 8000,
			Status:        model.TaskStatusInProgress,
		}

		mockTierInfo := &model.UserTierInfo{
			UserID:           testUserID,
			TradingVolumeUSD: decimal.NewFromFloat(12000), // Above 10K milestone
		}

		mockTask := &model.ActivityTask{
			ID:     testTaskID,
			Points: 300,
		}

		suite.mockService.On("GetTaskProgress", mock.Anything, testUserID, testTaskID).Return(mockProgress, nil)
		suite.mockService.On("GetUserTierInfo", mock.Anything, testUserID).Return(mockTierInfo, nil)
		suite.mockService.On("SetProgress", mock.Anything, testUserID, testTaskID, 12000).Return(nil)
		suite.mockService.On("CompleteTaskWithPoints", mock.Anything, testUserID, testTaskID, mock.MatchedBy(func(data map[string]interface{}) bool {
			milestone, ok := data["milestone"].(float64)
			return ok && milestone == 10000
		})).Return(nil)

		// Create processor
		processor := NewTradingTaskProcessor(suite.mockService)

		// Execute - call the method directly on TradingTaskProcessor
		tradeData := map[string]interface{}{
			"volume":     1000.0,
			"trade_type": "MEME",
		}

		// Cast to concrete type to access the method
		tradingProcessor, ok := processor.(*TradingTaskProcessor)
		suite.True(ok, "Should be able to cast to TradingTaskProcessor")

		err := tradingProcessor.processAccumulatedTrading(suite.ctx, testUserID, mockTask, tradeData, 10000)

		// Verify
		suite.NoError(err)
		suite.mockService.AssertExpectations(t)
	})

	// Test case: Non-MEME trade should be skipped
	suite.T().Run("Non-MEME trade should be skipped", func(t *testing.T) {
		// Reset mock
		suite.mockService = &MockActivityCashbackService{}

		mockTask := &model.ActivityTask{
			ID:     testTaskID,
			Points: 300,
		}

		// Create processor
		processor := NewTradingTaskProcessor(suite.mockService)

		// Execute
		tradeData := map[string]interface{}{
			"volume":     1000.0,
			"trade_type": "PERPETUAL", // Non-MEME trade
		}

		// Cast to concrete type to access the method
		tradingProcessor, ok := processor.(*TradingTaskProcessor)
		suite.True(ok, "Should be able to cast to TradingTaskProcessor")

		err := tradingProcessor.processAccumulatedTrading(suite.ctx, testUserID, mockTask, tradeData, 10000)

		// Verify
		suite.NoError(err)

		// Should NOT call any service methods for non-MEME trades
		suite.mockService.AssertNotCalled(t, "GetTaskProgress")
		suite.mockService.AssertNotCalled(t, "GetUserTierInfo")
		suite.mockService.AssertNotCalled(t, "SetProgress")
		suite.mockService.AssertNotCalled(t, "CompleteTaskWithPoints")
	})
}

func (suite *AccumulatedTradingLogicTestSuite) TestBatchCheckLogic() {
	testUserID := uuid.New()

	// Test batch check functionality
	suite.T().Run("Batch check multiple tasks", func(t *testing.T) {
		// Setup mock tier info with volume that should complete 10K and 50K tasks
		mockTierInfo := &model.UserTierInfo{
			UserID:           testUserID,
			TradingVolumeUSD: decimal.NewFromFloat(75000), // Should complete 10K and 50K
		}

		// Mock trading tasks
		mockTasks := []model.ActivityTask{
			{
				ID:             uuid.New(),
				TaskIdentifier: &[]model.TaskIdentifier{model.TaskIDAccumulatedTrading10K}[0],
				Points:         300,
			},
			{
				ID:             uuid.New(),
				TaskIdentifier: &[]model.TaskIdentifier{model.TaskIDAccumulatedTrading50K}[0],
				Points:         1000,
			},
			{
				ID:             uuid.New(),
				TaskIdentifier: &[]model.TaskIdentifier{model.TaskIDAccumulatedTrading100K}[0],
				Points:         2500,
			},
		}

		suite.mockService.On("GetUserTierInfo", mock.Anything, testUserID).Return(mockTierInfo, nil)
		suite.mockService.On("GetTasksByCategory", mock.Anything, model.CategoryTrading).Return(mockTasks, nil)

		// Mock progress for each task (not completed)
		for _, task := range mockTasks {
			mockProgress := &model.UserTaskProgress{
				UserID:        testUserID,
				TaskID:        task.ID,
				ProgressValue: 0,
				Status:        model.TaskStatusInProgress,
			}
			suite.mockService.On("GetTaskProgress", mock.Anything, testUserID, task.ID).Return(mockProgress, nil)
			suite.mockService.On("SetProgress", mock.Anything, testUserID, task.ID, 75000).Return(nil)
		}

		// Should complete 10K and 50K tasks (volume 75000 >= milestones)
		suite.mockService.On("CompleteTaskWithPoints", mock.Anything, testUserID, mockTasks[0].ID, mock.Anything).Return(nil) // 10K
		suite.mockService.On("CompleteTaskWithPoints", mock.Anything, testUserID, mockTasks[1].ID, mock.Anything).Return(nil) // 50K
		// Should NOT complete 100K task (volume 75000 < 100000)

		// Create processor
		processor := NewTradingTaskProcessor(suite.mockService)

		// Execute batch check - cast to concrete type to access the method
		tradingProcessor, ok := processor.(*TradingTaskProcessor)
		suite.True(ok, "Should be able to cast to TradingTaskProcessor")

		err := tradingProcessor.CheckAndUpdateAccumulatedTradingTasks(suite.ctx, testUserID)

		// Verify
		suite.NoError(err)
		suite.mockService.AssertExpectations(t)
	})
}

func TestAccumulatedTradingLogicTestSuite(t *testing.T) {
	suite.Run(t, new(AccumulatedTradingLogicTestSuite))
}
