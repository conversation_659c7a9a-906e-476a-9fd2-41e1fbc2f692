package activity_cashback

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/suite"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/test"
)

type AccumulatedTradingTasksTestSuite struct {
	suite.Suite
	service ActivityCashbackServiceInterface
	ctx     context.Context
}

func (suite *AccumulatedTradingTasksTestSuite) SetupSuite() {
	suite.ctx = context.Background()

	// Setup test configuration and database
	test.SetupTestWithDB(suite.T())

	// Initialize service
	suite.service = NewActivityCashbackService()

	// Seed categories and tasks
	err := suite.seedTestData()
	suite.NoError(err, "Should be able to seed test data")
}

func (suite *AccumulatedTradingTasksTestSuite) TearDownSuite() {
	test.TeardownTest()
}

func (suite *AccumulatedTradingTasksTestSuite) seedTestData() error {
	// First seed categories
	if err := suite.seedCategories(); err != nil {
		return err
	}

	// Then seed tasks
	seeder := NewTaskSeeder()
	return seeder.SeedTasks(suite.ctx)
}

func (suite *AccumulatedTradingTasksTestSuite) seedCategories() error {
	// Create admin service to seed categories
	adminService := NewAdminService()

	categories := []struct {
		name        model.TaskCategoryName
		displayName string
		description string
		icon        string
		sortOrder   int
	}{
		{
			name:        model.CategoryDaily,
			displayName: "Daily Tasks",
			description: "Tasks that can be completed daily",
			icon:        "📅",
			sortOrder:   1,
		},
		{
			name:        model.CategoryCommunity,
			displayName: "Community Tasks",
			description: "Social media and community engagement tasks",
			icon:        "👥",
			sortOrder:   2,
		},
		{
			name:        model.CategoryTrading,
			displayName: "Trading Tasks",
			description: "Trading-related tasks and milestones",
			icon:        "📈",
			sortOrder:   3,
		},
	}

	for _, cat := range categories {
		// Check if category already exists
		existingCategories, err := adminService.GetTaskCategories(suite.ctx)
		if err != nil {
			return err
		}

		exists := false
		for _, existing := range existingCategories {
			if existing.Name == cat.name {
				exists = true
				break
			}
		}

		if !exists {
			category := &model.TaskCategory{
				Name:        cat.name,
				DisplayName: cat.displayName,
				Description: &cat.description,
				Icon:        &cat.icon,
				IsActive:    true,
				SortOrder:   cat.sortOrder,
			}

			if err := adminService.CreateTaskCategory(suite.ctx, category); err != nil {
				return err
			}
		}
	}

	return nil
}

func (suite *AccumulatedTradingTasksTestSuite) TestAccumulatedTradingTasksFlow() {
	// Create test user
	testUserID := uuid.New()

	// Initialize user for activity cashback
	err := suite.service.InitializeUserForActivityCashback(suite.ctx, testUserID)
	suite.NoError(err, "Should be able to initialize user for activity cashback")

	// Set initial trading volume in user_tier_info
	tierInfo, err := suite.service.GetUserTierInfo(suite.ctx, testUserID)
	suite.NoError(err)

	// Test different volume levels
	testCases := []struct {
		volume        decimal.Decimal
		expectedTasks []model.TaskIdentifier
		description   string
	}{
		{
			volume:        decimal.NewFromFloat(5000),
			expectedTasks: []model.TaskIdentifier{}, // No tasks should be completed
			description:   "Volume below 10K - no tasks completed",
		},
		{
			volume: decimal.NewFromFloat(15000),
			expectedTasks: []model.TaskIdentifier{
				model.TaskIDAccumulatedTrading10K,
			},
			description: "Volume 15K - 10K task should be completed",
		},
		{
			volume: decimal.NewFromFloat(75000),
			expectedTasks: []model.TaskIdentifier{
				model.TaskIDAccumulatedTrading10K,
				model.TaskIDAccumulatedTrading50K,
			},
			description: "Volume 75K - 10K and 50K tasks should be completed",
		},
		{
			volume: decimal.NewFromFloat(150000),
			expectedTasks: []model.TaskIdentifier{
				model.TaskIDAccumulatedTrading10K,
				model.TaskIDAccumulatedTrading50K,
				model.TaskIDAccumulatedTrading100K,
			},
			description: "Volume 150K - 10K, 50K, and 100K tasks should be completed",
		},
		{
			volume: decimal.NewFromFloat(600000),
			expectedTasks: []model.TaskIdentifier{
				model.TaskIDAccumulatedTrading10K,
				model.TaskIDAccumulatedTrading50K,
				model.TaskIDAccumulatedTrading100K,
				model.TaskIDAccumulatedTrading500K,
			},
			description: "Volume 600K - all tasks should be completed",
		},
	}

	for _, tc := range testCases {
		suite.T().Run(tc.description, func(t *testing.T) {
			// Update trading volume
			tierInfo.TradingVolumeUSD = tc.volume
			err := suite.service.UpdateUserTierInfo(suite.ctx, tierInfo)
			suite.NoError(err)

			// Get trading task processor
			processorManager := NewTaskProcessorManager(suite.service)

			// Trigger batch check for accumulated trading tasks
			err = processorManager.CheckAndUpdateAllAccumulatedTradingTasks(suite.ctx, testUserID)
			suite.NoError(err)

			// Check which tasks are completed
			for _, taskIdentifier := range []model.TaskIdentifier{
				model.TaskIDAccumulatedTrading10K,
				model.TaskIDAccumulatedTrading50K,
				model.TaskIDAccumulatedTrading100K,
				model.TaskIDAccumulatedTrading500K,
			} {
				// Get task
				tradingTasks, err := suite.service.GetTasksByCategory(suite.ctx, model.CategoryTrading)
				suite.NoError(err)

				var task *model.ActivityTask
				for i := range tradingTasks {
					if tradingTasks[i].TaskIdentifier != nil && *tradingTasks[i].TaskIdentifier == taskIdentifier {
						task = &tradingTasks[i]
						break
					}
				}

				if task == nil {
					suite.T().Logf("Task %s not found", taskIdentifier)
					continue
				}

				// Check progress
				progress, err := suite.service.GetTaskProgress(suite.ctx, testUserID, task.ID)
				suite.NoError(err)

				// Check if task should be completed
				shouldBeCompleted := false
				for _, expectedTask := range tc.expectedTasks {
					if expectedTask == taskIdentifier {
						shouldBeCompleted = true
						break
					}
				}

				if shouldBeCompleted {
					suite.True(progress.IsCompleted(),
						"Task %s should be completed for volume %s",
						taskIdentifier, tc.volume.String())
					suite.T().Logf("✓ Task %s completed as expected", taskIdentifier)
				} else {
					suite.False(progress.IsCompleted(),
						"Task %s should NOT be completed for volume %s",
						taskIdentifier, tc.volume.String())
					suite.T().Logf("✓ Task %s not completed as expected", taskIdentifier)
				}
			}
		})
	}
}

func (suite *AccumulatedTradingTasksTestSuite) TestAccumulatedTradingHandlerDirectly() {
	// Create test user
	testUserID := uuid.New()

	// Initialize user for activity cashback
	err := suite.service.InitializeUserForActivityCashback(suite.ctx, testUserID)
	suite.NoError(err)

	// Set trading volume to 25K (should complete 10K task)
	tierInfo, err := suite.service.GetUserTierInfo(suite.ctx, testUserID)
	suite.NoError(err)
	tierInfo.TradingVolumeUSD = decimal.NewFromFloat(25000)
	err = suite.service.UpdateUserTierInfo(suite.ctx, tierInfo)
	suite.NoError(err)

	// Get 10K task
	tradingTasks, err := suite.service.GetTasksByCategory(suite.ctx, model.CategoryTrading)
	suite.NoError(err)

	var task10K *model.ActivityTask
	for i := range tradingTasks {
		if tradingTasks[i].TaskIdentifier != nil && *tradingTasks[i].TaskIdentifier == model.TaskIDAccumulatedTrading10K {
			task10K = &tradingTasks[i]
			break
		}
	}
	suite.NotNil(task10K, "10K task should exist")

	// Create handler and test directly
	handler := NewAccumulatedTradingHandler(suite.service, model.TaskIDAccumulatedTrading10K, 10000)

	// Call handler with empty data (it should check tier info directly)
	err = handler.Handle(suite.ctx, testUserID, task10K, map[string]interface{}{})
	suite.NoError(err)

	// Check if task is completed
	progress, err := suite.service.GetTaskProgress(suite.ctx, testUserID, task10K.ID)
	suite.NoError(err)
	suite.True(progress.IsCompleted(), "10K task should be completed by handler")

	suite.T().Logf("✓ AccumulatedTradingHandler completed 10K task successfully")
}

func TestAccumulatedTradingTasksTestSuite(t *testing.T) {
	suite.Run(t, new(AccumulatedTradingTasksTestSuite))
}
