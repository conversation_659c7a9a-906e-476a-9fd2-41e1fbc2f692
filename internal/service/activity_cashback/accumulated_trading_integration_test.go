package activity_cashback

import (
	"context"
	"fmt"
	"testing"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"go.uber.org/zap"
)

// AccumulatedTradingIntegrationTestSuite tests the integration without database
type AccumulatedTradingIntegrationTestSuite struct {
	suite.Suite
	mockService *MockActivityCashbackService
	ctx         context.Context
}

func (suite *AccumulatedTradingIntegrationTestSuite) SetupTest() {
	suite.ctx = context.Background()
	suite.mockService = &MockActivityCashbackService{}

	// Initialize logger to prevent nil pointer dereference
	if global.GVA_LOG == nil {
		global.GVA_LOG = zap.NewNop() // No-op logger for tests
	}
}

func (suite *AccumulatedTradingIntegrationTestSuite) TestAccumulatedTradingTasksExist() {
	// Test that all accumulated trading task identifiers exist
	expectedTasks := []model.TaskIdentifier{
		model.TaskIDAccumulatedTrading10K,
		model.TaskIDAccumulatedTrading50K,
		model.TaskIDAccumulatedTrading100K,
		model.TaskIDAccumulatedTrading500K,
	}

	for _, taskID := range expectedTasks {
		suite.T().Run(string(taskID), func(t *testing.T) {
			// Verify task identifier is not empty
			suite.NotEmpty(string(taskID), "Task identifier should not be empty")

			// Verify task identifier follows expected pattern
			suite.Contains(string(taskID), "ACCUMULATED_TRADING", "Task identifier should contain ACCUMULATED_TRADING")
		})
	}
}

func (suite *AccumulatedTradingIntegrationTestSuite) TestAccumulatedTradingHandlerCreation() {
	// Test that handlers can be created for all accumulated trading tasks
	testCases := []struct {
		identifier   model.TaskIdentifier
		targetVolume float64
	}{
		{model.TaskIDAccumulatedTrading10K, 10000},
		{model.TaskIDAccumulatedTrading50K, 50000},
		{model.TaskIDAccumulatedTrading100K, 100000},
		{model.TaskIDAccumulatedTrading500K, 500000},
	}

	for _, tc := range testCases {
		suite.T().Run(string(tc.identifier), func(t *testing.T) {
			handler := NewAccumulatedTradingHandler(suite.mockService, tc.identifier, tc.targetVolume)

			suite.NotNil(handler, "Handler should be created")
			suite.Equal(tc.identifier, handler.GetIdentifier(), "Handler should have correct identifier")
			suite.Equal("trading", handler.GetCategory(), "Handler should be in trading category")
		})
	}
}

func (suite *AccumulatedTradingIntegrationTestSuite) TestAccumulatedTradingWorkflow() {
	// Test the complete workflow: user starts with 0 volume, trades, and completes milestones
	testUserID := uuid.New()

	// Mock user tier info with progressive volume increases
	volumeProgression := []float64{5000, 15000, 75000, 150000, 600000}

	for _, volume := range volumeProgression {
		suite.T().Run(fmt.Sprintf("Volume_%.0f", volume), func(t *testing.T) {
			// Reset mock for each test
			mockService := &MockActivityCashbackService{}

			// Mock tier info with current volume
			mockTierInfo := &model.UserTierInfo{
				UserID:           testUserID,
				TradingVolumeUSD: decimal.NewFromFloat(volume),
			}
			mockService.On("GetUserTierInfo", mock.Anything, testUserID).Return(mockTierInfo, nil)

			// Mock trading tasks
			mockTasks := []model.ActivityTask{
				{
					ID:             uuid.New(),
					TaskIdentifier: &[]model.TaskIdentifier{model.TaskIDAccumulatedTrading10K}[0],
					Points:         300,
				},
				{
					ID:             uuid.New(),
					TaskIdentifier: &[]model.TaskIdentifier{model.TaskIDAccumulatedTrading50K}[0],
					Points:         1000,
				},
				{
					ID:             uuid.New(),
					TaskIdentifier: &[]model.TaskIdentifier{model.TaskIDAccumulatedTrading100K}[0],
					Points:         2500,
				},
				{
					ID:             uuid.New(),
					TaskIdentifier: &[]model.TaskIdentifier{model.TaskIDAccumulatedTrading500K}[0],
					Points:         10000,
				},
			}

			mockService.On("GetTasksByCategory", mock.Anything, model.CategoryTrading).Return(mockTasks, nil)

			// Expected completions based on volume
			expectedCompletions := []bool{
				volume >= 10000,  // 10K task
				volume >= 50000,  // 50K task
				volume >= 100000, // 100K task
				volume >= 500000, // 500K task
			}

			// Mock progress and completion calls
			for j, task := range mockTasks {
				mockProgress := &model.UserTaskProgress{
					UserID:        testUserID,
					TaskID:        task.ID,
					ProgressValue: 0,
					Status:        model.TaskStatusInProgress,
				}
				mockService.On("GetTaskProgress", mock.Anything, testUserID, task.ID).Return(mockProgress, nil)
				mockService.On("SetProgress", mock.Anything, testUserID, task.ID, int(volume)).Return(nil)

				if expectedCompletions[j] {
					mockService.On("CompleteTaskWithPoints", mock.Anything, testUserID, task.ID, mock.Anything).Return(nil)
				}
			}

			// Create processor and run batch check
			processor := NewTradingTaskProcessor(mockService)
			tradingProcessor, ok := processor.(*TradingTaskProcessor)
			suite.True(ok, "Should be able to cast to TradingTaskProcessor")

			// Execute batch check
			err := tradingProcessor.CheckAndUpdateAccumulatedTradingTasks(suite.ctx, testUserID)

			// Verify
			suite.NoError(err)
			mockService.AssertExpectations(t)

			// Log the expected behavior for this volume level
			completedCount := 0
			for _, completed := range expectedCompletions {
				if completed {
					completedCount++
				}
			}
			t.Logf("Volume %.0f should complete %d tasks", volume, completedCount)
		})
	}
}

func TestAccumulatedTradingIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(AccumulatedTradingIntegrationTestSuite))
}
